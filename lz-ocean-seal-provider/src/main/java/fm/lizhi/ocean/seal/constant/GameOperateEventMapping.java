package fm.lizhi.ocean.seal.constant;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 游戏操作事件映射
 * <AUTHOR>
 */
@Slf4j
@Getter
public enum GameOperateEventMapping {

    // region sud

    /**
     * 用户加入游戏
     */
    USER_IN(GameOperateEventType.USER_IN.getEvent(), GameChannel.SUD, "user_in"),

    /**
     * 用户退出游戏
     */
    USER_OUT(GameOperateEventType.USER_OUT.getEvent(), GameChannel.SUD, "user_out"),

    /**
     * 游戏开始
     */
    GAME_START(GameOperateEventType.GAME_START.getEvent(), GameChannel.SUD, "game_start"),

    /**
     * 游戏结束
     */
    GAME_END(GameOperateEventType.GAME_END.getEvent(), GameChannel.SUD, "game_end"),

    // endregion

    // region luk
    USER_IN_LUK(GameOperateEventType.USER_IN.getEvent(), GameChannel.LUK,  1),
    USER_OUT_LUK(GameOperateEventType.USER_OUT.getEvent(), GameChannel.LUK, 2),
    GAME_START_LUK(GameOperateEventType.GAME_START.getEvent(), GameChannel.LUK, 5),
    GAME_END_LUK(GameOperateEventType.GAME_END.getEvent(), GameChannel.LUK, 6),

    // endregion

    ;


    /**
     * 平台事件定义
     */
    private final String sealEvent;

    /**
     * 业务类型
     */
    private final String channel;

    /**
     * 渠道事件定义
     */
    private final Object channelEvent;


    GameOperateEventMapping(String sealEvent, String channel, Object channelEvent) {
        this.sealEvent = sealEvent;
        this.channel = channel;
        this.channelEvent = channelEvent;
    }

    /**
     * 渠道事件转平台事件
     *
     * @param channel      渠道名称
     * @param channelEvent 渠道事件
     * @return 平台事件
     */
    public static GameOperateEventType getSealEvent(String channel, Object channelEvent) {
        for (GameOperateEventMapping eventMapping : values()) {
            if (eventMapping.channel.equals(channel) && eventMapping.channelEvent.equals(channelEvent)) {
                return GameOperateEventType.from(eventMapping.sealEvent);
            }
        }
        return null;
    }

    /**
     * 平台事件转渠道事件
     *
     * @param channel   渠道名称
     * @param sealEvent 平台事件
     * @return 渠道事件
     */
    public static Object getChannelEvent(String channel, GameOperateEventType sealEvent) {
        for (GameOperateEventMapping eventMapping : values()) {
            if (eventMapping.channel.equals(channel) && eventMapping.sealEvent.equals(sealEvent.getEvent())) {
                return eventMapping.channelEvent;
            }
        }
        return null;
    }
}
