package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏渠道表
 *
 * @date 2025-07-01 02:15:43
 */
@Table(name = "`game_channel`")
public class GameChannelBean {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 渠道名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 渠道代号
     */
    @Column(name= "`channel`")
    private String channel;

    /**
     * 渠道appId
     */
    @Column(name= "`app_id`")
    private String appId;

    /**
     * 渠道appKey
     */
    @Column(name= "`app_key`")
    private String appKey;

    /**
     * 渠道appSecret
     */
    @Column(name= "`app_secret`")
    private String appSecret;

    /**
     * 基础请求URL
     */
    @Column(name= "`base_url`")
    private String baseUrl;

    /**
     * websocket url
     */
    @Column(name= "`ws_url`")
    private String wsUrl;

    /**
     * 扩展参数
     */
    @Column(name= "`ext_config`")
    private String extConfig;

    /**
     * 1-启用 0-禁用
     */
    @Column(name= "`state`")
    private Integer state;

    /**
     * 渠道备注信息，例如该渠道当前签约的主体信息
     */
    @Column(name= "`description`")
    private String description;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey == null ? null : appKey.trim();
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret == null ? null : appSecret.trim();
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl == null ? null : baseUrl.trim();
    }

    public String getWsUrl() {
        return wsUrl;
    }

    public void setWsUrl(String wsUrl) {
        this.wsUrl = wsUrl == null ? null : wsUrl.trim();
    }

    public String getExtConfig() {
        return extConfig;
    }

    public void setExtConfig(String extConfig) {
        this.extConfig = extConfig == null ? null : extConfig.trim();
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", channel=").append(channel);
        sb.append(", appId=").append(appId);
        sb.append(", appKey=").append(appKey);
        sb.append(", appSecret=").append(appSecret);
        sb.append(", baseUrl=").append(baseUrl);
        sb.append(", wsUrl=").append(wsUrl);
        sb.append(", extConfig=").append(extConfig);
        sb.append(", state=").append(state);
        sb.append(", description=").append(description);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}