package fm.lizhi.ocean.seal.strategy.impl;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.manager.GameCallbackManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.strategy.GameLoadingConfigStrategy;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.GetGameServiceListRequest;
import io.github.cfgametech.beans.GetGameServiceListResponse;
import io.github.cfgametech.beans.GetGameServiceListResponseEntry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * LUK 鸿运渠道游戏加载配置策略实现
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class LukGameLoadingConfigStrategy implements GameLoadingConfigStrategy {

    @Inject
    private GameChannelManager gameChannelManager;

    @Inject
    private GameCallbackManager gameCallbackManager;




    @Override
    public GameServiceProto.GameLoadingConfig getGameLoadingConfig(GameBizGameBean bizGameBean, GameInfoBean gameInfoBean, String appId) {
        try {
            log.info("Generating LUK game loading config for gameId: {}, channelGameId: {}", 
                    bizGameBean.getId(), gameInfoBean.getChannelGameIdStr());


            GetGameServiceListResponseEntry channelGame = getGameListByGameId(bizGameBean, gameInfoBean, appId);
            if(null == channelGame){
                log.warn("Failed to get channel game, gameId: {}, channelGameId: {}", bizGameBean.getId(), gameInfoBean.getChannelGameIdStr());
                return null;
            }

            return GameServiceProto.GameLoadingConfig.newBuilder()
                    .setGameId(gameInfoBean.getChannelGameId())
                    .setUrl(channelGame.getUrl())
                    .setGZipUrl(channelGame.getGZipUrl())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error generating LUK game loading config for gameId: {}", bizGameBean.getId(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }


    private GetGameServiceListResponseEntry getGameListByGameId(GameBizGameBean bizGame, GameInfoBean gameInfo, String appId){
        Long channelId = bizGame.getChannelId();
        Long gameId = bizGame.getId();

        try {

            Pair<SDK, GameChannelBean> pair = getLukSdk(channelId, gameId, appId);
            if(pair == null || pair.getLeft() == null || pair.getRight() == null){
                log.error("get luk sdk error.`channelId={}", channelId);
                return null;
            }
            SDK sdk = pair.getLeft();
            GameChannelBean gameChannel = pair.getRight();
            GetGameServiceListRequest request = new GetGameServiceListRequest.Builder()
                    .setChannelId(Integer.parseInt(gameChannel.getAppId()))
                    .setTimestamp(System.currentTimeMillis())
                    .build();
            Response<GetGameServiceListResponse> response = sdk.GetGameServiceList(request);
            if (response.suc()) {
                return response.getData().getGameList()
                        .stream()
                        .filter(x-> x.getId() == gameInfo.getChannelGameId().intValue())
                        .findFirst()
                        .orElse(null);
            }
        } catch (Exception e) {
            log.error("get game list error.`gameId={}`channelId={}", gameId, channelId, e);
        }
        return null;
    }


    /**
     * 获取 LUK 渠道的 SDK 实例
     *
     * @param channelId 渠道ID
     * @param gameId
     * @return SDK 实例
     */
    private Pair<SDK, GameChannelBean> getLukSdk(Long channelId, Long gameId, String appId){
        GameChannelBean gameChannel = gameChannelManager.getGameChannel(channelId);
        if(null == gameChannel){
            return null;
        }

        GameCallback gameCallback = gameCallbackManager.getGameCallback(appId, String.valueOf(gameId),
                gameCallbackManager.getCallBackTypeByGameInterface(), null
        );
        SDK sdk = new SDK(gameChannel.getAppSecret(), gameCallback.getUrl());
        return Pair.of(sdk, gameChannel);

    }

}
