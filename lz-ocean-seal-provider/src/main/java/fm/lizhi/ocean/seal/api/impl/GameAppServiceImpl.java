package fm.lizhi.ocean.seal.api.impl;

import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameAppService;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.GameCallbackManager;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 游戏App相关接口
 * <p>
 * Created in 2022-04-20 21:41.
 *
 * <AUTHOR>
 */
@ServiceProvider
public class GameAppServiceImpl implements GameAppService {
    private static final Logger logger = LoggerFactory.getLogger(GameAppServiceImpl.class);
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private GameCallbackManager gameCallbackManager;

    /**
     * 获取App回调配置信息
     *
     * @param param 参数
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 app不存在<br>
     * //if rcode == 3 内部错误<br>
     */
    @Override
    public Result<GameAppServiceProto.ResponseGetAppCallback> getAppCallback(GameAppServiceProto.GetAppCallbackParam param) {
        GameAppServiceProto.ResponseGetAppCallback.Builder builder = GameAppServiceProto.ResponseGetAppCallback.newBuilder();
        String appId = param.getAppId();
        LogContext.addReqLog("appId={}`type={}", appId, param.getType());
        GameCallback gameCallback = this.gameCallbackManager.getGameCallback(appId, null, param.getType(), null);
        if (gameCallback == null) {
            logger.error("failed to get game callback, appId:{}, type:{}", appId, param.getType());
            return new Result<>(GET_APP_CALLBACK_NOT_EXISTS, builder.build());
        }
        builder.setAppConfig(GameAppServiceProto.AppCallback.newBuilder()
                .setAppId(appId)
                .setType(gameCallback.getType())
                .setCallbackKey(gameCallback.getCallbackKey())
                .setCallbackGetUserUrl(gameCallback.getUrl())
                .build());
        return new Result<>(0, builder.build());
    }

    @Override
    public Result<ResponseGetAppInfoByAppId> getAppInfoByAppId(String appId) {
        LogContext.addReqLog("appId={}", appId);
        LogContext.addResLog("appId={}", appId);
        if(StringUtils.isBlank(appId)){
            return new Result<>(GET_APP_INFO_BY_APP_ID_ILLEGAL_PARAMS, null);
        }
        GameAppBean bean = gameAppManager.getGameAppBean(appId);
        if(null == bean){
            return new Result<>(GET_APP_INFO_BY_APP_ID_NOT_EXISTS, null);
        }

        List<GameChannelBean> relationChannels = gameAppManager.getGameChannelsByAppId(appId);
        if(CollectionUtils.isEmpty(relationChannels)){
            return new Result<>(GET_APP_INFO_BY_APP_ID_ERROR, null);
        }

        List<ChannelInfo> ChannelInfoPbs = relationChannels.stream().map(x-> ChannelInfo.newBuilder()
                .setId(x.getId()).setChannel(x.getChannel())
                .setBaseUrl(x.getBaseUrl())
                .setWsUrl(x.getWsUrl())
                .setChannelAppId(x.getAppId()).setChannelAppKey(x.getAppKey())
                .setChannelAppSecret(x.getAppSecret()).build()).collect(Collectors.toList());

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , ResponseGetAppInfoByAppId.newBuilder().setGameAppInfo(
                        GameAppServiceProto.GameAppInfo.newBuilder()
                                .setId(bean.getId())
                                .setAppName(Optional.ofNullable(bean.getAppName()).orElse(StringUtils.EMPTY))
                                .setAppAlias(Optional.ofNullable(bean.getAppAlias()).orElse(StringUtils.EMPTY))
                                .setAppId(Optional.ofNullable(bean.getAppId()).orElse(StringUtils.EMPTY))
                                .setAppSecret(Optional.ofNullable(bean.getAppSecret()).orElse(StringUtils.EMPTY))
                                .setAppTopic(Optional.ofNullable(bean.getAppTopic()).orElse(StringUtils.EMPTY))
                                .addAllChannelInfos(ChannelInfoPbs).build()).build());
    }
}