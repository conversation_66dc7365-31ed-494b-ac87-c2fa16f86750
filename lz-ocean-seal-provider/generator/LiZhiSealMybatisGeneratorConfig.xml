<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="mysql" targetRuntime="MyBatis3Simple">
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- datastore插件  mvn mybatis-generator:generate -->
        <plugin type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreMybatisPlugin">
            <property name="targetProject" value="src/main/java"/>
            <property name="targetPackage" value="fm.lizhi.ocean.seal.dao.mapper"/>
            <property name="mapperSuffix" value="Mapper"/>
            <property name="namespace" value="mysql_ocean_oceanseal"/>
        </plugin>

        <!-- 数据库实体类注释生成器 -->
        <commentGenerator type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreCommentGenerator">
        </commentGenerator>

        <!-- 数据库配置 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**************************************************************************************;
                        characterEncoding=UTF-8&amp;autoReconnect=true&amp;useSSL=false&amp;zeroDateTimeBehavior=convertToNull&amp;allowMultiQueries=true"
                        userId="root"
                        password="db_admin#ops.fm">
            <property name="useInformationSchema" value="true" />
        </jdbcConnection>

        <javaTypeResolver type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreJavaTypeResolver">
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 实体类生成器配置 -->
        <javaModelGenerator targetPackage="fm.lizhi.ocean.seal.dao.bean" targetProject="src/main/java">
            <property name="constructorBased" value="false"/>
            <property name="enableSubPackages" value="true" />
            <property name="immutable" value="false"/>
            <property name="trimStrings" value="true" />
        </javaModelGenerator>
        <table schema="ocean_seal" tableName="game_channel" domainObjectName="GameChannelBean" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
        <!--<table schema="ocean_seal" tableName="user_associated_code" domainObjectName="UserAssociatedCode" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table schema="ocean_seal" tableName="game_info" domainObjectName="GameInfoBean" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
        <table schema="ocean_seal" tableName="game_biz_game" domainObjectName="GameBizGameBean" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table schema="ocean_seal" tableName="game_biz_game" domainObjectName="GameBizGameBean" >-->
        <!--    <property name="useActualColumnNames" value="false"/>-->
        <!--</table>-->
        <!--<table schema="ocean_seal" tableName="game_round" domainObjectName="GameRoundBean" >-->
        <!--    <property name="useActualColumnNames" value="false"/>-->
        <!--</table>-->
        <!--<table schema="ocean_seal" tableName="game_info" domainObjectName="GameInfoBean" >-->
        <!--    <property name="useActualColumnNames" value="false"/>-->
        <!--    <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
        <!--</table>-->
        <!--<table schema="ocean_seal" tableName="game_biz_game_relation" domainObjectName="GameBizGameRelationBean" >-->
        <!--    <property name="useActualColumnNames" value="false"/>-->
        <!--</table>-->
        <!--<table schema="ocean_seal" tableName="game_version" domainObjectName="GameVersionBean" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table schema="ocean_seal" tableName="game_callback" domainObjectName="GameCallback" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table schema="ocean_seal" tableName="game_bill" domainObjectName="GameBillBean" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table schema="ocean_seal" tableName="game_sud_bill" domainObjectName="GameSudBillBean" >
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
<!--        <table schema="ocean_seal" tableName="game_round_result" domainObjectName="GameRoundResultBean" >-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--        </table>-->
    </context>
</generatorConfiguration>